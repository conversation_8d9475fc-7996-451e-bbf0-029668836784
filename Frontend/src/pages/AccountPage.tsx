import { useState, useEffect } from "react";
import { Mail, Save, AlertCircle } from "lucide-react";
import { userService } from "@/services/api";
import type { UserStats } from "@/types/api";
import { useToast } from "@/hooks/useToast";

export function AccountPage() {
  const [senderEmail, setSenderEmail] = useState("");
  const [receiverEmail, setReceiverEmail] = useState("");
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [fetching, setFetching] = useState(true);
  const [senderEmailError, setSenderEmailError] = useState("");
  const [receiverEmailError, setReceiverEmailError] = useState("");
  const [stats, setStats] = useState<UserStats>({ total_analyses: 0, hfos_detected: 0 });
  const { showToast } = useToast();

  // Fetch current preferences and stats
  useEffect(() => {
    fetchPreferences();
    fetchStats();
  }, []);

  const fetchPreferences = async () => {
    try {
      const preferences = await userService.getPreferences();
      // Handle backward compatibility
      setSenderEmail(preferences.sender_email || preferences.email || "<EMAIL>");
      setReceiverEmail(preferences.receiver_email || preferences.email || "<EMAIL>");
      setNotificationsEnabled(preferences.notification_enabled);
    } catch {
      // Silently handle errors - preferences are optional
      setSenderEmail("<EMAIL>");
      setReceiverEmail("<EMAIL>");
    } finally {
      setFetching(false);
    }
  };

  const fetchStats = async () => {
    try {
      const userStats = await userService.getStats();
      setStats(userStats);
    } catch {
      // Silently handle errors - stats are optional
    }
  };

  const validateEmail = (email: string, type: 'sender' | 'receiver') => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const setError = type === 'sender' ? setSenderEmailError : setReceiverEmailError;

    if (!email) {
      setError(`${type === 'sender' ? 'Sender' : 'Receiver'} email is required`);
      return false;
    }
    if (!emailRegex.test(email)) {
      setError("Please enter a valid email address");
      return false;
    }
    setError("");
    return true;
  };

  const handleSavePreferences = async () => {
    const isSenderValid = validateEmail(senderEmail, 'sender');
    const isReceiverValid = validateEmail(receiverEmail, 'receiver');

    if (!isSenderValid || !isReceiverValid) return;

    setLoading(true);
    try {
      await userService.updatePreferences({
        sender_email: senderEmail,
        receiver_email: receiverEmail,
        email: receiverEmail, // Backward compatibility
        notification_enabled: notificationsEnabled,
      });

      showToast("Preferences saved successfully", "success");
    } catch {
      showToast("Failed to save preferences", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyEmail = async () => {
    if (!validateEmail(receiverEmail, 'receiver')) return;

    setLoading(true);
    try {
      await userService.verifyEmail(receiverEmail);
      showToast("Verification email sent! Check your inbox.", "success");
    } catch {
      showToast("Failed to send verification email", "error");
    } finally {
      setLoading(false);
    }
  };

  if (fetching) {
    return (
      <div className="p-8 bg-white rounded-lg shadow-sm">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-6"></div>
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl">
      <div className="p-8 bg-white rounded-lg shadow-sm">
        <h1 className="text-2xl font-semibold mb-2">Account Settings</h1>
        <p className="text-gray-600 mb-8">Manage your notification preferences for HFO analysis</p>

        {/* Email Settings Section */}
        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <Mail className="w-5 h-5 mr-2" />
              Email Notifications
            </h2>

            <div className="space-y-4">
              {/* Sender Email Input */}
              <div>
                <label htmlFor="senderEmail" className="block text-sm font-medium text-gray-700 mb-1">
                  Sender Email (FROM address)
                </label>
                <input
                  type="email"
                  id="senderEmail"
                  value={senderEmail}
                  onChange={(e) => {
                    setSenderEmail(e.target.value);
                    setSenderEmailError("");
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    senderEmailError ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="<EMAIL>"
                />
                {senderEmailError && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {senderEmailError}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-500">Email address that will send analysis notifications (must be verified in AWS SES)</p>
              </div>

              {/* Receiver Email Input */}
              <div>
                <label htmlFor="receiverEmail" className="block text-sm font-medium text-gray-700 mb-1">
                  Receiver Email (TO address)
                </label>
                <div className="flex gap-2">
                  <input
                    type="email"
                    id="receiverEmail"
                    value={receiverEmail}
                    onChange={(e) => {
                      setReceiverEmail(e.target.value);
                      setReceiverEmailError("");
                    }}
                    className={`flex-1 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      receiverEmailError ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="<EMAIL>"
                  />
                  <button
                    onClick={handleVerifyEmail}
                    disabled={loading || !receiverEmail}
                    className="px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Verify
                  </button>
                </div>
                {receiverEmailError && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {receiverEmailError}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-500">You'll receive notifications at this email when your HFO analysis completes</p>
              </div>

              {/* Enable Notifications Toggle */}
              <div className="flex items-center justify-between py-4 border-t border-gray-200">
                <div>
                  <label htmlFor="notifications" className="font-medium text-gray-700">
                    Enable Email Notifications
                  </label>
                  <p className="text-sm text-gray-500">Receive emails when analysis jobs complete</p>
                </div>
                <button
                  id="notifications"
                  onClick={() => setNotificationsEnabled(!notificationsEnabled)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    notificationsEnabled ? "bg-blue-600" : "bg-gray-200"
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      notificationsEnabled ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                </button>
              </div>

              {/* Save Button */}
              <div className="pt-4 flex justify-end">
                <button
                  onClick={handleSavePreferences}
                  disabled={loading || !senderEmail || !receiverEmail}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Preferences
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Analysis Statistics */}
          <div className="pt-6 border-t border-gray-200">
            <h2 className="text-lg font-medium mb-4">Analysis Statistics</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Total Analyses</p>
                <p className="text-2xl font-semibold">{stats.total_analyses}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">HFOs Detected</p>
                <p className="text-2xl font-semibold">{stats.hfos_detected}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
