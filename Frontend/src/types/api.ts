export interface FileInfo {
  key: string;
  filename: string;
  size: number;
  last_modified: string;
  etag?: string;
}

export interface FileListResponse {
  files: FileInfo[];
  total_count: number;
  total_size: number;
}

export interface FileDeleteResponse {
  success: boolean;
  message: string;
  deleted_key: string;
}

export interface ApiError {
  detail: string;
  status?: number;
}

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
}

// Multipart upload types
export interface MultipartInitiateResponse {
  upload_id: string;
  key: string;
  expires_in: number;
}

export interface MultipartPartUrlResponse {
  url: string;
  expires_in: number;
}

export interface CompletedPart {
  ETag: string;
  PartNumber: number;
}

export interface MultipartBatchPartUrlsResponse {
  parts: { part_number: number; url: string }[];
  expires_in: number;
}

// User types
export interface UserPreferences {
  email?: string; // Deprecated, kept for backward compatibility
  sender_email: string;
  receiver_email: string;
  notification_enabled: boolean;
  last_updated: string;
}

export interface UserStats {
  total_analyses: number;
  hfos_detected: number;
}
